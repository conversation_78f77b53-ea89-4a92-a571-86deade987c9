<!-- Hero -->
<div class="bg-body-light">
    <div class="content content-full">
        <h1 class="h3 fw-bold mb-2">
            Gestion des UE
        </h1>
    </div>
</div>
<!-- END Hero -->

<!-- Page Content -->
<div class="content">
    <!-- Recherche unifiée et filtres rapides -->
    <div class="row mb-3">
        <div class="col-md-8">
            <div class="input-group">
                <input type="search" wire:model.debounce.300ms="query" class="form-control" 
                    placeholder="Rechercher par code, nom, parcours...">
                <button class="btn btn-alt-primary" type="button">
                    <i class="fa fa-search"></i>
                </button>
                <button class="btn btn-alt-secondary dropdown-toggle" type="button" id="dropdownFilters" 
                    data-bs-toggle="dropdown" aria-expanded="false">
                    Filtres <span class="badge bg-primary"><?php echo e($activeFiltersCount); ?></span>
                </button>
                <div class="dropdown-menu p-3" style="width: 300px" aria-labelledby="dropdownFilters">
                    <h6>Parcours</h6>
                    <div class="row g-2 mb-3">
                        <?php $__currentLoopData = $parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                        wire:model="selectedParcours" value="<?php echo e($parcour->id); ?>" 
                                        id="parcours<?php echo e($parcour->id); ?>">
                                    <label class="form-check-label" for="parcours<?php echo e($parcour->id); ?>">
                                        <?php echo e($parcour->sigle); ?>

                                    </label>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <h6>Niveau</h6>
                    <div class="row g-2 mb-3">
                        <?php $__currentLoopData = $niveaux; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                        wire:model="selectedNiveaux" value="<?php echo e($niveau->id); ?>" 
                                        id="niveau<?php echo e($niveau->id); ?>">
                                    <label class="form-check-label" for="niveau<?php echo e($niveau->id); ?>">
                                        <?php echo e($niveau->nom); ?>

                                    </label>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <h6>Année</h6>
                    <select wire:model="filtreAnnee" class="form-select form-select-sm">
                        <option value="">Toutes les années</option>
                        <?php $__currentLoopData = $annees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $annee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($annee->id); ?>"><?php echo e($annee->nom); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group">
                <button class="btn btn-alt-info" wire:click="openDuplicationModal" title="Dupliquer des UE vers d'autres parcours">
                    <i class="fa fa-copy me-1"></i> Dupliquer UE
                </button>
                <button class="btn btn-primary" wire:click="$toggle('showQuickAddModal')">
                    <i class="fa fa-plus me-1"></i> Ajouter UE
                </button>
            </div>
        </div>
    </div>

    <!-- Liste des UE avec accordéon pour EC -->
    <div class="block block-rounded">
        <div class="block-content p-0">
            <div class="table-responsive">
                <table class="table table-striped table-vcenter mb-0">
                    <thead>
                        <tr>
                            <th style="width: 40px;"></th>
                            <th class="text-center" style="width: 100px;">Code</th>
                            <th>Nom</th>
                            <th style="width: 80px;">Crédit</th>
                            <th>Parcours & Niveau</th>
                            <th style="width: 120px;">Semestre</th>
                            <th style="width: 200px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $ues; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ue): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td class="text-center">
                                    <button class="btn btn-sm btn-alt-secondary" 
                                        wire:click="toggleEcList(<?php echo e($ue->id); ?>)">
                                        <i class="fa fa-<?php echo e(in_array($ue->id, $expandedUes) ? 'minus' : 'plus'); ?>"></i>
                                    </button>
                                </td>
                                <td class="text-center fw-semibold">
                                    <?php if($editingUeId === $ue->id): ?>
                                        <input type="text" wire:model.defer="editUe.code" class="form-control form-control-sm">
                                    <?php else: ?>
                                        <?php echo e($ue->code); ?>

                                    <?php endif; ?>
                                </td>
                                <td class="fw-semibold">
                                    <?php if($editingUeId === $ue->id): ?>
                                        <input type="text" wire:model.defer="editUe.nom" class="form-control form-control-sm">
                                    <?php else: ?>
                                        <?php echo e($ue->nom); ?>

                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($editingUeId === $ue->id): ?>
                                        <input type="number" wire:model.defer="editUe.credit" class="form-control form-control-sm">
                                    <?php else: ?>
                                        <?php echo e($ue->credit); ?>

                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($editingUeId === $ue->id): ?>
                                        <div class="row g-2">
                                            <div class="col-6">
                                                <select wire:model.defer="editUe.parcour_id" class="form-select form-select-sm">
                                                    <?php $__currentLoopData = $parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($parcour->id); ?>"><?php echo e($parcour->sigle); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                            <div class="col-6">
                                                <select wire:model.defer="editUe.niveau_id" class="form-select form-select-sm">
                                                    <?php $__currentLoopData = $niveaux; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($niveau->id); ?>"><?php echo e($niveau->nom); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <?php echo e($ue->parcours->sigle); ?> <?php echo e($ue->niveau->nom); ?>

                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($editingUeId === $ue->id): ?>
                                        <select wire:model.defer="editUe.semestre_id" class="form-select form-select-sm">
                                            <?php $__currentLoopData = $semestres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $semestre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($semestre->id); ?>"><?php echo e($semestre->nom); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    <?php else: ?>
                                        <?php echo e($ue->semestre->nom); ?>

                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($editingUeId === $ue->id): ?>
                                        <div class="btn-group w-100">
                                            <button type="button" class="btn btn-sm btn-success" wire:click="updateUe">
                                                <i class="fa fa-check"></i> Enregistrer
                                            </button>
                                            <button type="button" class="btn btn-sm btn-alt-secondary" wire:click="cancelEdit">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                    <?php else: ?>
                                        <div class="btn-group">
                                            <button type="button" wire:click="startEditing(<?php echo e($ue->id); ?>)"
                                                class="btn btn-sm btn-alt-secondary" title="Modifier UE">
                                                <i class="fa fa-pencil-alt"></i>
                                            </button>
                                            <button type="button" wire:click="addQuickEC(<?php echo e($ue->id); ?>)"
                                                class="btn btn-sm btn-alt-primary" title="Ajouter EC">
                                                <i class="fa fa-plus"></i> EC
                                            </button>
                                            <button type="button" wire:click="deleteUe(<?php echo e($ue->id); ?>)"
                                                class="btn btn-sm btn-alt-danger" title="Supprimer"
                                                onclick="confirm('Êtes-vous sûrs?') || event.stopImmediatePropagation()">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                    <?php endif; ?>
                                </td>
                            </tr>

                            <!-- Sous-tableau pour les ECs - Visible quand l'UE est expanded -->
                            <?php if(in_array($ue->id, $expandedUes)): ?>
                                <tr>
                                    <td colspan="7" class="p-0 border-0">
                                        <div class="bg-body-light p-3">
                                            <h6 class="mb-3">Eléments Constitutifs (EC) - <?php echo e($ue->nom); ?></h6>
                                            
                                            <?php if($ue->matiere->count() > 0): ?>
                                                <table class="table table-sm table-bordered">
                                                    <thead>
                                                        <tr>
                                                            <th>Code</th>
                                                            <th>Nom</th>
                                                            <th>Enseignant</th>
                                                            <th style="width: 150px;">Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php $__currentLoopData = $ue->matiere; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $matiere): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <tr>
                                                                <td><?php echo e($matiere->code); ?></td>
                                                                <td><?php echo e($matiere->nom); ?></td>
                                                                <td>
                                                                    <?php if($matiere->user): ?>
                                                                        <?php echo e($matiere->user->nom); ?> <?php echo e($matiere->user->prenom); ?>

                                                                    <?php else: ?>
                                                                        <span class="badge bg-warning">Non assigné</span>
                                                                    <?php endif; ?>
                                                                </td>
                                                                <td class="text-center">
                                                                    <div class="btn-group">
                                                                        <button type="button" wire:click="editEC(<?php echo e($matiere->id); ?>)"
                                                                            class="btn btn-xs btn-alt-secondary">
                                                                            <i class="fa fa-pencil-alt"></i>
                                                                        </button>
                                                                        
                                                                        <button type="button" wire:click="deleteEC(<?php echo e($matiere->id); ?>)"
                                                                            class="btn btn-xs btn-alt-danger"
                                                                            onclick="confirm('Êtes-vous sûrs?') || event.stopImmediatePropagation()">
                                                                            <i class="fa fa-times"></i>
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </tbody>
                                                </table>
                                            <?php else: ?>
                                                <div class="alert alert-info mb-0">
                                                    <i class="fa fa-info-circle me-1"></i> Aucun EC ajouté pour cette UE.
                                                    <button class="btn btn-sm btn-alt-info float-end" 
                                                        wire:click="addQuickEC(<?php echo e($ue->id); ?>)">
                                                        <i class="fa fa-plus me-1"></i> Ajouter EC
                                                    </button>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <?php if($ues->count() == 0): ?>
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">Aucune UE trouvée avec les critères actuels</div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="block-content">
            <nav aria-label="Pagination">
                <ul class="pagination pagination-sm justify-content-end mt-2">
                    <?php echo e($ues->links()); ?>

                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Modal Ajout Rapide UE -->
<?php if($showQuickAddModal): ?>
<div class="modal fade show d-block" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="block block-rounded block-transparent mb-0">
                <div class="block-header block-header-default">
                    <h3 class="block-title">Ajouter une UE</h3>
                    <div class="block-options">
                        <button type="button" class="btn-block-option" wire:click="$toggle('showQuickAddModal')">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="block-content">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Code</label>
                            <input type="text" class="form-control" wire:model.defer="newUe.code" placeholder="Code UE">
                            <?php $__errorArgs = ['newUe.code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Crédit</label>
                            <input type="number" class="form-control" wire:model.defer="newUe.credit" placeholder="Nombre de crédits">
                            <?php $__errorArgs = ['newUe.credit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Nom</label>
                        <input type="text" class="form-control" wire:model.defer="newUe.nom" placeholder="Nom de l'UE">
                        <?php $__errorArgs = ['newUe.nom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Parcours</label>
                            <select class="form-select" wire:model.defer="newUe.parcour_id">
                                <option value="">Sélectionner un parcours</option>
                                <?php $__currentLoopData = $parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($parcour->id); ?>"><?php echo e($parcour->sigle); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['newUe.parcour_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Niveau</label>
                            <select class="form-select" wire:model.defer="newUe.niveau_id">
                                <option value="">Sélectionner un niveau</option>
                                <?php $__currentLoopData = $niveaux; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($niveau->id); ?>"><?php echo e($niveau->nom); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['newUe.niveau_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Semestre</label>
                            <select class="form-select" wire:model.defer="newUe.semestre_id">
                                <option value="">Sélectionner un semestre</option>
                                <?php $__currentLoopData = $semestres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $semestre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($semestre->id); ?>"><?php echo e($semestre->nom); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['newUe.semestre_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Année</label>
                            <select class="form-select" wire:model.defer="newUe.annee_universitaire_id">
                                <option value="">Sélectionner une année</option>
                                <?php $__currentLoopData = $annees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $annee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($annee->id); ?>"><?php echo e($annee->nom); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['newUe.annee_universitaire_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>
                <div class="block-content block-content-full block-content-sm text-end border-top">
                    <button type="button" class="btn btn-alt-secondary" wire:click="$toggle('showQuickAddModal')">
                        Annuler
                    </button>
                    <button type="button" class="btn btn-primary" wire:click="addUe()">
                        <i class="fa fa-check opacity-50 me-1"></i> Ajouter
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal-backdrop fade show"></div>
<?php endif; ?>


<!-- Modal Ajout/Édition Rapide EC -->
<?php if($showEcModal): ?>
<div class="modal fade show d-block" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="block block-rounded block-transparent mb-0">
                <div class="block-header block-header-default">
                    <h3 class="block-title"><?php echo e($ecModalMode === 'add' ? 'Ajouter' : 'Modifier'); ?> un EC</h3>
                    <div class="block-options">
                        <button type="button" class="btn-block-option" wire:click="closeEcModal">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="block-content">
                    <div class="row">
                        <!-- Colonne de gauche: Informations EC -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">UE</label>
                                <input type="text" class="form-control bg-gray-100" readonly 
                                    value="<?php echo e($currentEcUe ? $currentEcUe->code . ' - ' . $currentEcUe->nom : ''); ?>">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Code EC</label>
                                <input type="text" class="form-control" wire:model.defer="ecForm.code" placeholder="Code EC">
                                <?php $__errorArgs = ['ecForm.code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="mb-4">
                                <label class="form-label">Nom EC</label>
                                <input type="text" class="form-control" wire:model.defer="ecForm.nom" placeholder="Nom de l'EC">
                                <?php $__errorArgs = ['ecForm.nom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        
                        <!-- Colonne de droite: Assignation Enseignant -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <label class="form-label mb-0">Enseignant</label>
                                    <button type="button" class="btn btn-sm btn-alt-primary" wire:click="toggleAddEnseignantForm">
                                        <i class="fa fa-plus me-1"></i> Nouveau
                                    </button>
                                </div>
                                
                                <!-- Affiche l'enseignant actuellement sélectionné -->
                                <?php if(isset($ecForm['user_id']) && !empty($ecForm['user_id'])): ?>
                                    <?php
                                        $selectedEnseignant = App\Models\User::find($ecForm['user_id']);
                                    ?>
                                    <?php if($selectedEnseignant): ?>
                                        <div class="alert alert-success p-2 mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-grow-1">
                                                    <strong><?php echo e($selectedEnseignant->nom); ?> <?php echo e($selectedEnseignant->prenom); ?></strong>
                                                    <div class="small"><?php echo e($selectedEnseignant->email); ?></div>
                                                </div>
                                                <button type="button" class="btn btn-sm btn-alt-danger" 
                                                    wire:click="$set('ecForm.user_id', '')">
                                                    <i class="fa fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                                
                                <!-- Recherche d'enseignant -->
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Rechercher un enseignant..." 
                                        wire:model.debounce.300ms="enseignantQuery">
                                    <button class="btn btn-alt-secondary" type="button">
                                        <i class="fa fa-search"></i>
                                    </button>
                                </div>
                                
                                <!-- Résultats de recherche -->
                                <?php if($filteredEnseignants && count($filteredEnseignants) > 0): ?>
                                    <div class="list-group mt-2 mb-3">
                                        <?php $__currentLoopData = $filteredEnseignants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $enseignant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" 
                                                wire:click="selectEnseignant(<?php echo e($enseignant->id); ?>)">
                                                <div>
                                                    <span class="fw-bold"><?php echo e($enseignant->nom); ?> <?php echo e($enseignant->prenom); ?></span>
                                                    <small class="d-block text-muted"><?php echo e($enseignant->email); ?></small>
                                                </div>
                                                <?php if(isset($ecForm['user_id']) && $enseignant->id == $ecForm['user_id']): ?>
                                                    <span class="badge bg-primary rounded-pill"><i class="fa fa-check"></i></span>
                                                <?php endif; ?>
                                            </button>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                <?php elseif($enseignantQuery && strlen($enseignantQuery) >= 2): ?>
                                    <div class="text-center text-muted my-3">
                                        <p>Aucun enseignant trouvé</p>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Enseignants récents -->
                                <?php if(count($recentlyAssignedEnseignants) > 0): ?>
                                    <div class="mb-3">
                                        <label class="form-label">Enseignants récents</label>
                                        <div class="row g-2">
                                            <?php $__currentLoopData = $recentlyAssignedEnseignants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ensId): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php
                                                    $e = App\Models\User::find($ensId);
                                                ?>
                                                <?php if($e): ?>
                                                    <div class="col-6">
                                                        <button type="button" 
                                                            class="btn btn-sm btn-block w-100 text-start <?php if(isset($ecForm['user_id']) && $ensId == $ecForm['user_id']): ?> btn-primary <?php else: ?> btn-alt-secondary <?php endif; ?>"
                                                            wire:click="selectEnseignant(<?php echo e($ensId); ?>)">
                                                            <div class="text-truncate"><?php echo e($e->nom); ?> <?php echo e($e->prenom); ?></div>
                                                        </button>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Liste déroulante complète -->
                                <div class="mb-3">
                                    <label class="form-label">Ou sélectionner dans la liste</label>
                                    <select class="form-select" wire:model="ecForm.user_id">
                                        <option value="">-- Sélectionner un enseignant --</option>
                                        <?php $__currentLoopData = $enseignants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $enseignant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($enseignant->id); ?>">
                                                <?php echo e($enseignant->nom); ?> <?php echo e($enseignant->prenom); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Formulaire d'ajout d'un nouvel enseignant -->
                    <?php if($showAddEnseignantForm): ?>
                        <div class="alert alert-info">
                            <h5 class="mb-3">Ajouter un nouvel enseignant</h5>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Nom</label>
                                    <input type="text" class="form-control" wire:model.defer="newEnseignant.nom">
                                    <?php $__errorArgs = ['newEnseignant.nom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Prénom</label>
                                    <input type="text" class="form-control" wire:model.defer="newEnseignant.prenom">
                                    <?php $__errorArgs = ['newEnseignant.prenom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            
                            
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Téléphone</label>
                                    <input type="text" class="form-control" wire:model.defer="newEnseignant.telephone1">
                                    <?php $__errorArgs = ['newEnseignant.telephone1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Sexe</label>
                                    <select class="form-select" wire:model.defer="newEnseignant.sexe">
                                        <option value="M">Homme</option>
                                        <option value="F">Femme</option>
                                    </select>
                                    <?php $__errorArgs = ['newEnseignant.sexe'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            
                            <div class="text-end">
                                <button type="button" class="btn btn-alt-secondary" wire:click="toggleAddEnseignantForm">
                                    Annuler
                                </button>
                                <button type="button" class="btn btn-alt-success" wire:click="createEnseignant">
                                    <i class="fa fa-check me-1"></i> Créer enseignant
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="block-content block-content-full block-content-sm text-end border-top">
                    <button type="button" class="btn btn-alt-secondary" wire:click="closeEcModal">
                        Annuler
                    </button>
                    <button type="button" class="btn btn-primary" wire:click="saveEC">
                        <i class="fa fa-check opacity-50 me-1"></i> 
                        <?php echo e($ecModalMode === 'add' ? 'Ajouter' : 'Mettre à jour'); ?>

                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal-backdrop fade show"></div>
<?php endif; ?>

<!-- Include Duplication Modal -->
<?php echo $__env->make('livewire.deraq.ue.duplication-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<script>
    // Select2 initialization for duplication modal
    document.addEventListener('livewire:load', function () {
        initDuplicationSelect2();

        // Reinitialize Select2 when Livewire refreshes the DOM
        Livewire.hook('message.processed', (message, component) => {
            setTimeout(() => {
                initDuplicationSelect2();
            }, 50);
        });

        // Close modal after successful duplication
        window.addEventListener('closeDuplicationModalAfterDelay', event => {
            setTimeout(() => {
                window.livewire.find('<?php echo e($_instance->id); ?>').call('closeDuplicationModal');
            }, 2000);
        });
    });

    function initDuplicationSelect2() {
        // Parcours multi-select for duplication
        if (document.getElementById('duplication-parcours-select')) {
            $('#duplication-parcours-select').select2({
                placeholder: 'Sélectionnez les parcours de destination',
                allowClear: true,
                closeOnSelect: false, // Keep dropdown open after selection
                width: '100%',
                dropdownParent: $('.modal-content'), // Ensure dropdown appears within modal
                dropdownCssClass: 'select2-dropdown-fixed'
            }).on('change', function (e) {
                // Don't update Livewire immediately - wait for blur event
            }).on('blur', function (e) {
                // Sync with Livewire when focus leaves the select element
                window.livewire.find('<?php echo e($_instance->id); ?>').set('targetParcours', $(this).val() || []);
            }).on('select2:close', function (e) {
                // Alternative approach - update when dropdown closes
                setTimeout(() => {
                    window.livewire.find('<?php echo e($_instance->id); ?>').set('targetParcours', $(this).val() || []);
                }, 100);
            });

            // Set initial values if any
            if (window.livewire.find('<?php echo e($_instance->id); ?>').targetParcours && window.livewire.find('<?php echo e($_instance->id); ?>').targetParcours.length > 0) {
                $('#duplication-parcours-select').val(window.livewire.find('<?php echo e($_instance->id); ?>').targetParcours).trigger('change');
            }
        }
        // Parcours multi-select for duplication
        if (document.getElementById('duplication-parcours-select1')) {
            $('#duplication-parcours-select1').select2({
                placeholder: 'Sélectionnez les parcours de destination',
                allowClear: true,
                closeOnSelect: false, // Keep dropdown open after selection
                width: '100%',
                dropdownParent: $('.modal-content'), // Ensure dropdown appears within modal
                dropdownCssClass: 'select2-dropdown-fixed'
            }).on('change', function (e) {
                // Don't update Livewire immediately - wait for blur event
            }).on('blur', function (e) {
                // Sync with Livewire when focus leaves the select element
                window.livewire.find('<?php echo e($_instance->id); ?>').set('duplicationFilterParcours', $(this).val() || []);
            }).on('select2:close', function (e) {
                // Alternative approach - update when dropdown closes
                setTimeout(() => {
                    window.livewire.find('<?php echo e($_instance->id); ?>').set('duplicationFilterParcours', $(this).val() || []);
                }, 100);
            });

            // Set initial values if any
            if (window.livewire.find('<?php echo e($_instance->id); ?>').duplicationFilterParcours && window.livewire.find('<?php echo e($_instance->id); ?>').duplicationFilterParcours.length > 0) {
                $('#duplication-parcours-select1').val(window.livewire.find('<?php echo e($_instance->id); ?>').duplicationFilterParcours).trigger('change');
            }
        }
    }

    // Prevent Livewire from updating while Select2 dropdown is open
    window.addEventListener('livewire:before-dom-update', () => {
        if ($('.select2-dropdown').is(':visible')) {
            $('.select2-container').select2('close');
        }
    });
</script>

<style>
    /* Custom styles for Select2 in modal */
    .select2-dropdown-fixed {
        z-index: 9999 !important;
    }

    .select2-container--default .select2-selection--multiple {
        min-height: 38px;
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice {
        background-color: #3498db;
        border: 1px solid #2980b9;
        color: white;
        border-radius: 0.25rem;
        padding: 2px 8px;
        margin: 2px;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
        color: white;
        margin-right: 5px;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
        color: #e74c3c;
    }

    /* Duplication modal specific styles */
    .duplication-ue-list {
        max-height: 350px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
    }

    .duplication-ue-item {
        transition: background-color 0.2s ease;
        border-bottom: 1px solid #f8f9fa;
    }

    .duplication-ue-item:hover {
        background-color: #f8f9fa;
    }

    .duplication-ue-item:last-child {
        border-bottom: none;
    }

    .duplication-filter-card {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
    }

    .duplication-stats {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
    }
</style>
<?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/livewire/deraq/ue/liste.blade.php ENDPATH**/ ?>