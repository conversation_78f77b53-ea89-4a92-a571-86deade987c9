<!-- UE/EC Duplication Modal -->
<?php if($showDuplicationModal): ?>
<div class="modal fade show d-block" tabindex="-1" role="dialog" aria-hidden="true" wire:ignore.self>
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fa fa-copy me-2"></i>
                    Duplication d'UE et EC
                    <?php if($duplicationStep === 'selection'): ?>
                        - Sélection
                    <?php elseif($duplicationStep === 'preview'): ?>
                        - Aperçu et Modification
                    <?php elseif($duplicationStep === 'processing'): ?>
                        - Traitement en cours...
                    <?php endif; ?>
                </h5>
                <button type="button" class="btn-close btn-close-white" wire:click="closeDuplicationModal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <?php if($duplicationStep === 'selection'): ?>
                    <!-- Step 1: Selection -->
                    <div class="row">
                        <!-- Left Column: UE Selection -->
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">
                                <i class="fa fa-list-check me-1"></i>
                                Sélectionner les UE à dupliquer
                            </h6>

                            <div class="alert alert-info">
                                <i class="fa fa-info-circle me-1"></i>
                                Sélectionnez une ou plusieurs UE à dupliquer. Les EC associés seront également dupliqués.
                            </div>

                            <!-- Filtres pour la sélection des UEs -->
                            <div class="card mb-3 duplication-filter-card">
                                <div class="card-header py-2">
                                    <h6 class="mb-0">
                                        <i class="fa fa-filter me-1"></i>
                                        Filtres de sélection
                                        <button class="btn btn-sm btn-outline-secondary float-end" wire:click="clearDuplicationFilters">
                                            <i class="fa fa-times me-1"></i>Effacer
                                        </button>
                                    </h6>
                                </div>
                                <div class="card-body py-2">
                                    <!-- Recherche -->
                                    <div class="mb-2">
                                        <input type="search" wire:model.debounce.300ms="duplicationQuery"
                                            class="form-control form-control-sm"
                                            placeholder="Rechercher par code, nom, parcours...">
                                    </div>

                                    <div class="row g-2">
                                        <!-- Filtre Année -->
                                        <div class="col-6">
                                            <select wire:model="duplicationFilterAnnee" class="form-select form-select-sm">
                                                <option value="">Toutes les années</option>
                                                <?php $__currentLoopData = $annees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $annee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($annee->id); ?>"><?php echo e($annee->nom); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>

                                        <!-- Filtre Parcours -->
                                        <div class="col-6">
                                            <select  id="duplication-parcours-select1" class="form-select form-select-sm" multiple>
                                                <?php $__currentLoopData = $parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($parcour->id); ?>"><?php echo e($parcour->sigle); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Sélection tout/rien -->
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                        wire:model="selectAllUes"
                                        wire:click="toggleSelectAllUes"
                                        id="selectAllUes">
                                    <label class="form-check-label fw-bold" for="selectAllUes">
                                        Tout sélectionner/désélectionner
                                    </label>
                                </div>
                                <?php if(count($selectedUesForDuplication) > 0): ?>
                                    <span class="badge bg-success">
                                        <?php echo e(count($selectedUesForDuplication)); ?> sélectionnée(s)
                                    </span>
                                <?php endif; ?>
                            </div>

                            <!-- Liste des UEs filtrées -->
                            <div class="duplication-ue-list p-3">
                                <?php
                                    $filteredUes = $this->getFilteredUesForDuplication();
                                ?>

                                <?php if($filteredUes->count() > 0): ?>
                                    <?php $__currentLoopData = $filteredUes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ue): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="form-check mb-2 duplication-ue-item p-2">
                                            <input class="form-check-input" type="checkbox"
                                                wire:click="toggleUeSelection(<?php echo e($ue->id); ?>)"
                                                <?php if(in_array($ue->id, $selectedUesForDuplication)): ?> checked <?php endif; ?>
                                                id="ue_<?php echo e($ue->id); ?>">
                                            <label class="form-check-label w-100" for="ue_<?php echo e($ue->id); ?>">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <strong><?php echo e($ue->code); ?></strong> - <?php echo e($ue->nom); ?>

                                                        <div class="small text-muted">
                                                            <?php echo e($ue->parcours->sigle); ?> <?php echo e($ue->niveau->nom); ?> - <?php echo e($ue->semestre->nom); ?>

                                                            <span class="badge bg-secondary ms-1"><?php echo e($ue->matiere->count()); ?> EC(s)</span>
                                                            <span class="badge bg-info ms-1"><?php echo e($ue->annee->nom); ?></span>
                                                        </div>
                                                    </div>
                                                    <span class="badge bg-primary"><?php echo e($ue->credit); ?> crédits</span>
                                                </div>
                                            </label>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <div class="text-center text-muted py-3">
                                        <i class="fa fa-search fa-2x mb-2"></i>
                                        <p>Aucune UE trouvée avec les critères actuels</p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <?php if(count($selectedUesForDuplication) > 0): ?>
                                <div class="duplication-stats mt-3">
                                    <i class="fa fa-check me-1"></i>
                                    <strong><?php echo e(count($selectedUesForDuplication)); ?> UE(s) sélectionnée(s)</strong> pour la duplication
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Right Column: Target Selection -->
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">
                                <i class="fa fa-target me-1"></i>
                                Destination de la duplication
                            </h6>

                            <!-- Academic Year Selection -->
                            <div class="mb-4">
                                <label class="form-label fw-semibold">Année universitaire de destination <span class="text-danger">*</span></label>
                                <select class="form-select" wire:model="targetAnneeId">
                                    <option value="">Sélectionner une année...</option>
                                    <?php $__currentLoopData = $annees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $annee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($annee->id); ?>"><?php echo e($annee->nom); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            <!-- Parcours Multi-Selection -->
                            <div class="mb-4">
                                <label class="form-label fw-semibold">Parcours de destination <span class="text-danger">*</span></label>
                                <select class="form-select" id="duplication-parcours-select" multiple wire:ignore>
                                    <?php $__currentLoopData = $parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($parcour->id); ?>"><?php echo e($parcour->sigle); ?> - <?php echo e($parcour->nom); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <div class="form-text">
                                    Utilisez Ctrl+Clic pour sélectionner plusieurs parcours
                                </div>
                            </div>

                            <?php if(count($targetParcours) > 0): ?>
                                <div class="alert alert-success">
                                    <i class="fa fa-check me-1"></i>
                                    <?php echo e(count($targetParcours)); ?> parcours sélectionné(s)
                                </div>
                            <?php endif; ?>

                            <!-- Summary -->
                            <?php if(count($selectedUesForDuplication) > 0 && count($targetParcours) > 0): ?>
                                <div class="alert alert-primary">
                                    <h6 class="mb-2">Résumé de la duplication :</h6>
                                    <ul class="mb-0">
                                        <li><?php echo e(count($selectedUesForDuplication)); ?> UE(s) à dupliquer</li>
                                        <li><?php echo e(count($targetParcours)); ?> parcours de destination</li>
                                        <li><strong><?php echo e(count($selectedUesForDuplication) * count($targetParcours)); ?></strong> UE(s) seront créées</li>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                <?php elseif($duplicationStep === 'preview'): ?>
                    <!-- Step 2: Preview and Edit -->
                    <div class="alert alert-warning">
                        <i class="fa fa-exclamation-triangle me-1"></i>
                        <strong>Aperçu avant duplication</strong> - Vous pouvez modifier les données avant de confirmer la duplication.
                    </div>

                    <div class="accordion" id="previewAccordion">
                        <?php $__currentLoopData = $duplicationPreview; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ueId => $preview): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading<?php echo e($ueId); ?>">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" 
                                        data-bs-target="#collapse<?php echo e($ueId); ?>" aria-expanded="true" aria-controls="collapse<?php echo e($ueId); ?>">
                                        <strong><?php echo e($preview['ue']['code']); ?> - <?php echo e($preview['ue']['nom']); ?></strong>
                                        <span class="badge bg-primary ms-2"><?php echo e(count($preview['ecs'])); ?> EC(s)</span>
                                        <?php if(count($preview['conflicts']) > 0): ?>
                                            <span class="badge bg-warning ms-1"><?php echo e(count($preview['conflicts'])); ?> conflit(s)</span>
                                        <?php endif; ?>
                                    </button>
                                </h2>
                                <div id="collapse<?php echo e($ueId); ?>" class="accordion-collapse collapse show" 
                                    aria-labelledby="heading<?php echo e($ueId); ?>" data-bs-parent="#previewAccordion">
                                    <div class="accordion-body">
                                        <!-- UE Data -->
                                        <div class="row mb-4">
                                            <div class="col-md-6">
                                                <h6 class="fw-bold">Données UE</h6>
                                                <div class="mb-2">
                                                    <label class="form-label">Code</label>
                                                    <input type="text" class="form-control form-control-sm" 
                                                        wire:model="editablePreviewData.<?php echo e($ueId); ?>.ue.code">
                                                </div>
                                                <div class="mb-2">
                                                    <label class="form-label">Nom</label>
                                                    <input type="text" class="form-control form-control-sm" 
                                                        wire:model="editablePreviewData.<?php echo e($ueId); ?>.ue.nom">
                                                </div>
                                                <div class="mb-2">
                                                    <label class="form-label">Crédit</label>
                                                    <input type="number" class="form-control form-control-sm" 
                                                        wire:model="editablePreviewData.<?php echo e($ueId); ?>.ue.credit">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <h6 class="fw-bold">Parcours de destination</h6>
                                                <ul class="list-group list-group-flush">
                                                    <?php $__currentLoopData = $preview['target_parcours']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <li class="list-group-item px-0 py-1">
                                                            <i class="fa fa-arrow-right me-1 text-primary"></i>
                                                            <?php echo e($parcour['sigle']); ?> - <?php echo e($parcour['nom']); ?>

                                                        </li>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </ul>
                                            </div>
                                        </div>

                                        <!-- Conflicts Warning -->
                                        <?php if(count($preview['conflicts']) > 0): ?>
                                            <div class="alert alert-warning">
                                                <h6 class="fw-bold">Conflits détectés :</h6>
                                                <?php $__currentLoopData = $preview['conflicts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $conflict): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div>
                                                        <i class="fa fa-exclamation-triangle me-1"></i>
                                                        <?php echo e($conflict['message']); ?> (<?php echo e($conflict['parcours_nom']); ?>)
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>

                                        <!-- EC Data -->
                                        <?php if(count($preview['ecs']) > 0): ?>
                                            <h6 class="fw-bold mb-3">Éléments Constitutifs (EC)</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm table-bordered">
                                                    <thead>
                                                        <tr>
                                                            <th>Code</th>
                                                            <th>Nom</th>
                                                            <th>Enseignant</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php $__currentLoopData = $preview['ecs']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $ec): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <tr>
                                                                <td>
                                                                    <input type="text" class="form-control form-control-sm" 
                                                                        wire:model="editablePreviewData.<?php echo e($ueId); ?>.ecs.<?php echo e($index); ?>.code">
                                                                </td>
                                                                <td>
                                                                    <input type="text" class="form-control form-control-sm" 
                                                                        wire:model="editablePreviewData.<?php echo e($ueId); ?>.ecs.<?php echo e($index); ?>.nom">
                                                                </td>
                                                                <td>
                                                                    <select class="form-select form-select-sm" 
                                                                        wire:model="editablePreviewData.<?php echo e($ueId); ?>.ecs.<?php echo e($index); ?>.user_id">
                                                                        <option value="">-- Non assigné --</option>
                                                                        <?php $__currentLoopData = $enseignants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $enseignant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <option value="<?php echo e($enseignant->id); ?>">
                                                                                <?php echo e($enseignant->nom); ?> <?php echo e($enseignant->prenom); ?>

                                                                            </option>
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                <?php elseif($duplicationStep === 'processing'): ?>
                    <!-- Step 3: Processing -->
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">Traitement en cours...</span>
                        </div>
                        <h5>Duplication en cours...</h5>
                        <p class="text-muted">Veuillez patienter pendant la création des UE et EC.</p>
                    </div>
                <?php endif; ?>
            </div>

            <div class="modal-footer">
                <?php if($duplicationStep === 'selection'): ?>
                    <button type="button" class="btn btn-secondary" wire:click="closeDuplicationModal">
                        <i class="fa fa-times me-1"></i>Annuler
                    </button>
                    <button type="button" class="btn btn-primary" wire:click="proceedToPreview"
                        <?php if(count($selectedUesForDuplication) === 0 || count($targetParcours) === 0 || empty($targetAnneeId)): ?> disabled <?php endif; ?>>
                        <i class="fa fa-arrow-right me-1"></i>Aperçu
                    </button>
                <?php elseif($duplicationStep === 'preview'): ?>
                    <button type="button" class="btn btn-secondary" wire:click="backToSelection">
                        <i class="fa fa-arrow-left me-1"></i>Retour
                    </button>
                    <button type="button" class="btn btn-success" wire:click="executeDuplication">
                        <i class="fa fa-copy me-1"></i>Confirmer la duplication
                    </button>
                <?php elseif($duplicationStep === 'processing'): ?>
                    <button type="button" class="btn btn-secondary" disabled>
                        <i class="fa fa-spinner fa-spin me-1"></i>Traitement...
                    </button>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<div class="modal-backdrop fade show"></div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/livewire/deraq/ue/duplication-modal.blade.php ENDPATH**/ ?>